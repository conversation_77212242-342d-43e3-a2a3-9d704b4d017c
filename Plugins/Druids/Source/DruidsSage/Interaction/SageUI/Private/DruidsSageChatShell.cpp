#include "DruidsSageChatShell.h"
#include "DruidsSageChatView.h"
#include "ISageCodeExecutor.h"

// UMG includes
#include "Blueprint/UserWidget.h"

//////////////////////////////////////////////////////////////////////////
// UDruidsSageChatShell - UMG implementation
//////////////////////////////////////////////////////////////////////////

UDruidsSageChatShell::UDruidsSageChatShell(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer), ChatView(nullptr)
{
}

void UDruidsSageChatShell::NativePreConstruct()
{
	Super::NativePreConstruct();
}

void UDruidsSageChatShell::NativeConstruct()
{
	Super::NativeConstruct();

	if (!IsDesignTime())
	{
		// Initialize the chat view after construction
		InitializeChatView();
	}
}

void UDruidsSageChatShell::SynchronizeProperties()
{
	Super::SynchronizeProperties();

	// Properties are set during construction and initialization
	// No additional synchronization needed
}

void UDruidsSageChatShell::ReleaseSlateResources(bool bReleaseChildren)
{
	Super::ReleaseSlateResources(bReleaseChildren);

	OnMessageSending.Unbind();
}

void UDruidsSageChatShell::SetChatRequestHandler(const TSharedPtr<IChatRequestHandler>& InChatRequestHandler)
{
	ChatRequestHandler = InChatRequestHandler;

	// Update the chat view if it's already created
	if (ChatView)
	{
		ChatView->SetChatRequestHandler(ChatRequestHandler);
	}
}

void UDruidsSageChatShell::SetExtensionDelegator(const TSharedPtr<ISageExtensionDelegator>& InExtensionDelegator)
{
	ExtensionDelegator = InExtensionDelegator;

	// Update the chat view if it's already created
	if (ChatView)
	{
		ChatView->SetExtensionsDelegator(ExtensionDelegator);
	}
}

void UDruidsSageChatShell::SetCodeExecutor(const TSharedPtr<ISageCodeExecutor>& InCodeExecutor)
{
	CodeExecutor = InCodeExecutor;

	// Update the chat view if it's already created
	if (ChatView)
	{
		ChatView->SetCodeExecutor(CodeExecutor);
	}
}

UDruidsSageChatView* UDruidsSageChatShell::GetCurrentView() const
{
	return ChatView;
}

void UDruidsSageChatShell::InitializeChatView()
{
	if (!ChatView)
	{
		return;
	}

	ChatView->OnMessageSending.AddDynamic(this, &UDruidsSageChatShell::OnChatViewMessageSending);

	if (ChatRequestHandler.IsValid())
	{
		ChatView->SetChatRequestHandler(ChatRequestHandler);
	}

	if (ExtensionDelegator.IsValid())
	{
		ChatView->SetExtensionsDelegator(ExtensionDelegator);
	}

	if (CodeExecutor.IsValid())
	{
		ChatView->SetCodeExecutor(CodeExecutor);
	}
}

void UDruidsSageChatShell::OnChatViewMessageSending()
{
	OnMessageSending.ExecuteIfBound();
}

