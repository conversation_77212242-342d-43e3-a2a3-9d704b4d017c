#pragma once

#include "CoreMinimal.h"

// Forward declarations
struct FSagePythonExecuteResult;

struct SAGECORE_API FSageActionResult
{
    /** Whether any action was called (extension or code executor) */
    bool bActionCalled;
    
    /** Whether the action succeeded */
    bool bSuccess;
    
    /** Result message from the action */
    FString Result;
    
    /** Error message if the action failed */
    FString ErrorMessage;

    FSageActionResult()
        : bActionCalled(false)
        , bSuccess(false)
        , Result(TEXT(""))
        , ErrorMessage(TEXT(""))
    {
    }

    /** Constructor for successful action */
    FSageActionResult(bool bInActionCalled, bool bInSuccess, const FString& InResult)
        : bActionCalled(bInActionCalled)
        , bSuccess(bInSuccess)
        , Result(InResult)
        , ErrorMessage(TEXT(""))
    {
    }

    /** Constructor for failed action */
    FSageActionResult(bool bInActionCalled, bool bInSuccess, const FString& InResult, const FString& InErrorMessage)
        : bActionCalled(bInActionCalled)
        , bSuccess(bInSuccess)
        , Result(InResult)
        , ErrorMessage(InErrorMessage)
    {
    }

    /** Create result from Python execution result */
    static FSageActionResult FromPythonResult(const FSagePythonExecuteResult& PythonResult);

    /** Create result for no action taken */
    static FSageActionResult NoAction()
    {
        return FSageActionResult(false, false, TEXT(""), TEXT(""));
    }

    /** Create result for successful extension action */
    static FSageActionResult ExtensionSuccess(const FString& InResult = TEXT(""))
    {
        return FSageActionResult(true, true, InResult);
    }

    /** Create result for failed extension action */
    static FSageActionResult ExtensionFailure(const FString& InErrorMessage)
    {
        return FSageActionResult(true, false, TEXT(""), InErrorMessage);
    }
};
