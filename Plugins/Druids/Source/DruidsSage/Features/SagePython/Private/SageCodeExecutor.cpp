#include "SageCodeExecutor.h"

#include "SagePythonWorker.h"
#include "SagePythonTypes.h"

FSageActionResult FSageCodeExecutor::OnActionApplied(const TSharedPtr<FJsonValue>& ActionDetails)
{
	if (const TSharedPtr<FJsonObject> *ContentObject;
		ActionDetails->TryGetObject(ContentObject) && ContentObject->IsValid())
	{
		if (FString CodeGuid;
			ContentObject->Get()->TryGetStringField(TEXT("code_guid"), CodeGuid))
		{
			// Get the Python worker instance and execute the code
			USagePythonWorker* Worker = USagePythonWorker::GetInstance();
			if (Worker)
			{
				FSagePythonExecuteResult Result = Worker->ExecutePythonCodeByGuid(CodeGuid);

				if (Result.bSuccess)
				{
					UE_LOG(LogTemp, Log, TEXT("FSageCodeExecutor: Successfully executed Python code with GUID: %s"), *CodeGuid);
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("FSageCodeExecutor: Failed to execute Python code with GUID: %s, Error: %s"), *CodeGuid, *Result.ErrorMessage);
				}

				// Convert Python result to generic action result
				return FSageActionResult::FromPythonResult(Result);
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("FSageCodeExecutor: Failed to get Python worker instance"));
				return FSageActionResult(true, false, TEXT(""), TEXT("Failed to get Python worker instance"));
			}
		}
	}

	// No code action was taken
	return FSageActionResult::NoAction();
}
