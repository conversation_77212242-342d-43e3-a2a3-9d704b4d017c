#pragma once

#include "CoreMinimal.h"

struct FSagePythonCodeEntry
{
    FString CodeGuid;
    FString PythonCode;
    bool bIsCompiled;
    FString CompilationError;
    FDateTime CreatedTime;
    FDateTime LastModifiedTime;

    FSagePythonCodeEntry()
        : CodeGuid(FGuid::NewGuid().ToString())
        , PythonCode(TEXT(""))
        , bIsCompiled(false)
        , CompilationError(TEXT(""))
        , CreatedTime(FDateTime::Now())
        , LastModifiedTime(FDateTime::Now())
    {
    }

    explicit FSagePythonCodeEntry(const FString& InPythonCode)
        : CodeGuid(FGuid::NewGuid().ToString())
        , PythonCode(InPythonCode)
        , bIsCompiled(false)
        , CompilationError(TEXT(""))
        , CreatedTime(FDateTime::Now())
        , LastModifiedTime(FDateTime::Now())
    {
    }
};

struct SAGEPYTHON_API FSagePythonCompileResult
{
    FString CodeGuid;
    bool bSuccess;
    FString ErrorMessage;
    FString ErrorDetails;

    FSagePythonCompileResult()
        : CodeGuid(TEXT(""))
        , bSuccess(false)
        , ErrorMessage(TEXT(""))
        , ErrorDetails(TEXT(""))
    {
    }
};

struct SAGEPYTHON_API FSagePythonExecuteResult
{
    FString CodeGuid;
    bool bSuccess;
    FString Result;
    FString ErrorMessage;

    FSagePythonExecuteResult()
        : CodeGuid(TEXT(""))
        , bSuccess(false)
        , Result(TEXT(""))
        , ErrorMessage(TEXT(""))
    {
    }
};


