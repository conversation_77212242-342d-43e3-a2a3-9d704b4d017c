#include "SageExtensionDelegator.h"

#include "SageExtension.h"
#include "ActiveSageExtensions.h"
#include "DruidsSage/Features/SagePython/Public/SagePythonTypes.h"

FSageActionResult FSageExtensionDelegator::OnActionApplied(const TSharedPtr<FJsonValue>& ActionDetails)
{
	if (const TSharedPtr<FJsonObject> *ContentObject;
		ActionDetails->TryGetObject(ContentObject) && ContentObject->IsValid())
	{
		if (FString ExtensionId;
			ContentObject->Get()->TryGetStringField(TEXT("extension_id"), ExtensionId))
		{
			TWeakObjectPtr<USageExtension> DruidsSageExtension =
				FActiveSageExtensions::Get().GetExtensionForId(ExtensionId);
			if (DruidsSageExtension.IsValid())
			{
				try
				{
					DruidsSageExtension.Get()->ExecuteAction(ContentObject->ToWeakPtr());
					return FSageActionResult::ExtensionSuccess(FString::Printf(TEXT("Extension '%s' executed successfully"), *ExtensionId));
				}
				catch (const std::exception& e)
				{
					FString ErrorMessage = FString::Printf(TEXT("Extension '%s' execution failed: %s"), *ExtensionId, UTF8_TO_TCHAR(e.what()));
					return FSageActionResult::ExtensionFailure(ErrorMessage);
				}
				catch (...)
				{
					FString ErrorMessage = FString::Printf(TEXT("Extension '%s' execution failed with unknown error"), *ExtensionId);
					return FSageActionResult::ExtensionFailure(ErrorMessage);
				}
			}
			else
			{
				FString ErrorMessage = FString::Printf(TEXT("Extension with ID '%s' not found or invalid"), *ExtensionId);
				return FSageActionResult::ExtensionFailure(ErrorMessage);
			}
		}
	}

	// No extension action was taken
	return FSageActionResult::NoAction();
}

TSharedPtr<FJsonObject> FSageExtensionDelegator::OnQueryRequested(const TSharedPtr<FJsonObject>& QueryRequestObject)
{
	if (FString ExtensionId;
	QueryRequestObject.Get()->TryGetStringField(TEXT("extension_id"), ExtensionId))
	{
		TWeakObjectPtr<USageExtension> DruidsSageExtension =
			FActiveSageExtensions::Get().GetExtensionForId(ExtensionId);
		if (DruidsSageExtension.IsValid())
		{
			DruidsSageExtension.Get()->ExecuteQuery(QueryRequestObject.ToWeakPtr());

			if (const TSharedPtr<FJsonObject> *ResultsObject; QueryRequestObject->TryGetObjectField(TEXT("results"), ResultsObject))
			{
				return *ResultsObject;
			}
		}
	}

	return nullptr;
}
