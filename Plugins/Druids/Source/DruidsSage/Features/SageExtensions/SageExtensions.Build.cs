using UnrealBuildTool;

public class SageExtensions : ModuleRules
{
    public SageExtensions(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core", 
                "Blutility",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                
                "UnrealEd",
                
                "Json",
            }
        );
        
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "DruidsCore",

                "SageCommonTypes",
                "SageCore",
            }
        );
    }
}